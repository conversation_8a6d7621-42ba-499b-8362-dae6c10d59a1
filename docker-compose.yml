version: "3.8"

services:
  #mysql数据库脚本
  mysql:
    image: mysql:5.7
    container_name: mysql
    restart: always
    ports:
      - 3306:3306
    volumes:
      - ./mysql/mydir:/mydir
      - ./mysql/data:/var/lib/mysql
      - ./mysql/conf/my.cnf:/etc/my.cnf
      - ./mysql/source:/docker-entrypoint-initdb.d/
    environment:
      MYSQL_ROOT_PASSWORD: pass
    networks:
      extnetwork:
        ipv4_address: **********
  #nacos服务脚本
  nacos:
    image: nacos/nacos-server:2.0.2
    container_name: nacos
    restart: always
    ports:
      - "8848:8848"
    environment:
      SPRING_DATASOURCE_PLATFORM: mysql #数据源平台 仅支持mysql或不保存empty
      MODE: standalone
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: pass
      NACOS_APPLICATION_PORT: 8848
      JVM_XMS: 512m
      JVM_MMS: 256m
      JVM_XMN: 128m
    networks:
      extnetwork:
        ipv4_address: **********
    depends_on:
      - mysql
  #seata服务脚本
  seata-server:
    image: seataio/seata-server:1.5.2
    container_name: seata-server
    restart: always
    ports:
      - "9200:9200"
      - "7091:7091"
      - "8091:8091"
    volumes:
      - ./seata-server:/seata-server/resources
    environment:
      SEATA_IP: ***************
      SEATA_PORT: 9200
    networks:
      extnetwork:
        ipv4_address: **********
    depends_on:
      - nacos
  #rabbitmq脚本
  rabbitmq:
    image: rabbitmq:3.8.3-management
    container_name: rabbitmq
    restart: always
    ports:
      - 15672:15672
      - 5672:5672
    volumes:
      - ./rabbitmq/data:/var/lib/rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: pass
    networks:
      extnetwork:
        ipv4_address: **********
  xxl-job:
    image: xuxueli/xxl-job-admin:2.1.2
    container_name: xxl-job-admin
    restart: always
    ports:
      - 8280:8080
    volumes:
      - ./xxl-job/data:/data/applogs
    environment:
      PARAMS: "--spring.datasource.url=*********************************************************************************************************************** --spring.datasource.username=root --spring.datasource.password=pass"
    networks:
      extnetwork:
        ipv4_address: **********
    depends_on:
      - mysql
  redis:
    image: redis:5.0.0
    container_name: redis
    restart: always
    command: redis-server --requirepass pass
    ports:
      - 6379:6379
    volumes:
      - ./redis/data:/data
    networks:
      extnetwork:
        ipv4_address: ***********
  influxdb:
    image: influxdb:1.8.0
    container_name: influxdb
    restart: always
    ports:
      - 9083:8083
      - 8086:8086
      - 8088:8088
    privileged: true
    volumes:
      - ./influxdb/data/influxdb:/var/lib/influxdb
      - ./influxdb/config/influxdb.conf:/etc/influxdb/influxdb.conf
# docker容器内网地址
networks:
  extnetwork:
    name: extnetwork
    ipam:
      config:
        - subnet: **********/16
