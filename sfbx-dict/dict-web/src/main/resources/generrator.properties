#\u6570\u636E\u5E93\u5730\u5740
url=*******************************************************************************************************************************
#\u6570\u636E\u5E93\u8D26\u53F7
userName=root
#\u6570\u636E\u5E93\u5BC6\u7801
password=pass
#\u6B64\u5904\u4E3A\u672C\u9879\u76EEsrc\u6240\u5728\u8DEF\u5F84\uFF08\u4EE3\u7801\u751F\u6210\u5668\u8F93\u51FA\u8DEF\u5F84\uFF09
serviceProjectPath=D:/easy-cloud/easy-dict/dict-web
#\u8BBE\u7F6E\u4F5C\u8005
author=Admin
#\u81EA\u5B9A\u4E49\u5305\u8DEF\u5F84
parent=com.itheima
#\u88C5\u4EE3\u7801\u7684\u6587\u4EF6\u5939\u540D
moduleName=project
#\u8BBE\u7F6E\u8868\u524D\u7F00\uFF0C\u4E0D\u8BBE\u7F6E\u5219\u9ED8\u8BA4\u65E0\u524D\u7F00
tablePrefix =tab_
#\u6570\u636E\u5E93\u8868\u540D(\u6B64\u5904\u5207\u4E0D\u53EF\u4E3A\u7A7A\uFF0C\u5982\u679C\u4E3A\u7A7A\uFF0C\u5219\u9ED8\u8BA4\u8BFB\u53D6\u6570\u636E\u5E93\u7684\u6240\u6709\u8868\u540D)
tableName=tab_data_dict,tab_places
#pojo\u7684\u8D85\u7C7B
SuperEntityClass = com.itheima.sfbx.framework.commons.basic.BasicPojo
#pojo\u7684\u8D85\u7C7B\u516C\u7528\u5B57\u6BB5
superEntityColumns = id,created_time,updated_time,sharding_id,enable_flag
#\u751F\u6210\u7684\u5C42\u7EA7
entity=true
entity.ftl.path=/templates/entity.java
mapper=true
mapper.ftl.path=/templates/mapper.java
service=false
service.ftl.path=/templates/service.java
serviceImp=false
serviceImp.ftl.path=/templates/serviceImpl.java
controller=false
controller.ftl.path=/templates/controller.java
